import asyncio
from agents import <PERSON>, Agent, OpenAIChatCompletionsModel
from openai import AsyncAzureOpenAI

async def debug_runner():
    try:
        custom_client = AsyncAzureOpenAI(
            api_key='test-key',
            azure_endpoint='https://test.openai.azure.com',
            api_version="2024-12-01-preview"
        )
        
        agent = Agent(
            name="TestAgent",
            instructions="Test agent",
            model=OpenAIChatCompletionsModel(model='gpt-4o', openai_client=custom_client)
        )
        
        print("About to call Runner.run...")
        result = await Runner.run(
            starting_agent=agent,
            input="test input",
            context="",
            max_turns=1
        )
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"Error type: {type(e)}")
        print(f"Error message: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_runner())