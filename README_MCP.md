# 多 MCP 服务调用框架

本项目实现了一个完整的多 MCP (Model Context Protocol) 服务调用框架，支持 Python 3.11，可以同时管理和调用多个 MCP 服务。

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保使用 Python 3.11
python --version  # 应该显示 Python 3.11.x

# 安装依赖
pip install -r requirements_mcp.txt
```

### 2. 环境变量配置

创建 `.env` 文件：

```bash
# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=coupon_db

# 优惠券生成密钥
SECRET_KEY=your_secret_key_in_hex

# RAG 服务
RAG_URL=http://*************:5008/

# Azure OpenAI 配置
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com
AZURE_OPENAI_MODEL=gpt-4o
```

### 3. 启动所有 MCP 服务

```bash
# 步骤2: 逐一启动所有 MCP 服务
python start_all_services.py
```

输出示例：
```
🚀 Starting service: google_maps
   Command: python src/google_map.py
   URL: http://127.0.0.1:8000/mcp
⏳ Waiting for service 'google_maps' to be ready...
✅ Service 'google_maps' started successfully

🚀 Starting service: coupon
   Command: python src/coupon_mcp.py
   URL: http://127.0.0.1:8001/mcp
⏳ Waiting for service 'coupon' to be ready...
✅ Service 'coupon' started successfully

📊 Service startup summary: 2/2 services started
```

### 4. 运行客户端示例

```bash
# 运行完整示例
python example_usage.py
```

## 📋 六个步骤详解

### 步骤1: 确认每个 MCP 服务的启动方式

在 `mcp_services_config.py` 中配置：

```python
MCP_SERVICES_CONFIG = {
    "google_maps": {
        "name": "GoogleMapsMCPServer",
        "type": "http",  # stdio, http, sse
        "url": "http://127.0.0.1:8000/mcp",
        "host": "127.0.0.1",
        "port": 8000,
        "startup_command": "python src/google_map.py",
        # ... 其他配置
    },
    "coupon": {
        "name": "CouponMCPServer", 
        "type": "http",
        "url": "http://127.0.0.1:8001/mcp",
        "host": "127.0.0.1", 
        "port": 8001,
        "startup_command": "python src/coupon_mcp.py",
        # ... 其他配置
    }
}
```

### 步骤2: 逐一启动所有 MCP 服务

```python
# 使用 MCPServiceManager 启动服务
manager = MCPServiceManager()
manager.start_all_services()
```

### 步骤3: 准备一个 MCP 客户端

```python
from multi_mcp_client import MultiMCPClient

client = MultiMCPClient()
```

### 步骤4: 初始化客户端并获取服务提供的工具列表

```python
# 初始化客户端
await client.initialize()

# 自动获取所有服务的工具列表
# 工具注册表: tools_registry = {"tool_name": "service_name"}
```

### 步骤5: 调用具体工具

```python
# 直接调用工具
result = await client.call_tool("geocode", address="北京市天安门广场")

# 或使用 Agent 智能调用
response = await client.run_agent("请帮我查询北京天安门的经纬度")
```

### 步骤6: 处理返回结果

```python
# 标准化结果处理
processed_result = client.process_result(result)
print(processed_result)
# {
#   "success": True,
#   "data": {"latitude": 39.9042, "longitude": 116.4074},
#   "type": "json"
# }
```

## 🛠️ 可用的 MCP 服务和工具

### Google Maps 服务 (端口 8000)
- `geocode(address: str)` - 地理编码，获取地址的经纬度
- `get_nearby_stores(address: str)` - 获取附近商店信息

### 优惠券服务 (端口 8001)
- `generate_coupon(company_id, country_code, channel, contact_type, contact_value)` - 生成优惠券

### RAG 知识库服务 (端口 8002)
- `search_knowledge(company_id, keywords, top_k)` - 知识库搜索
- `get_company_info(company_id)` - 获取公司信息
- `search_faq(company_id, question)` - FAQ 搜索

## 💡 使用示例

### 示例1: 直接工具调用

```python
import asyncio
from multi_mcp_client import MultiMCPClient

async def example():
    async with MultiMCPClient().managed_client() as client:
        # 地理编码
        result = await client.call_tool(
            "geocode", 
            address="北京市天安门广场"
        )
        print(f"经纬度: {result}")
        
        # 生成优惠券
        coupon = await client.call_tool(
            "generate_coupon",
            company_id=1,
            country_code="CN", 
            channel="website",
            contact_type="mobile",
            contact_value="+86138000000"
        )
        print(f"优惠券: {coupon}")

asyncio.run(example())
```

### 示例2: Agent 智能对话

```python
async def agent_example():
    async with MultiMCPClient().managed_client() as client:
        response = await client.run_agent(
            "我在北京天安门广场，请帮我找附近的商店并生成一张优惠券，"
            "我的手机号是+86138000000，通过网站访问"
        )
        print(f"Agent 回复: {response}")

asyncio.run(agent_example())
```

### 示例3: 便捷函数

```python
from multi_mcp_client import quick_call, quick_agent_run

# 快速工具调用
result = await quick_call("geocode", address="上海外滩")

# 快速 Agent 对话
response = await quick_agent_run("请查询上海外滩的经纬度")
```

## 🔧 配置说明

### 服务配置

每个服务支持以下配置项：

```python
{
    "name": "服务名称",
    "type": "连接类型 (http/stdio/sse)",
    "url": "服务URL",
    "host": "主机地址", 
    "port": "端口号",
    "timeout": "超时时间(秒)",
    "retries": "重试次数",
    "disabled": "是否禁用",
    "startup_command": "启动命令",
    "working_directory": "工作目录",
    "env_vars": {"环境变量": "值"}
}
```

### 客户端配置

```python
CLIENT_CONFIG = {
    "timeout": 60,
    "max_retries": 3,
    "retry_delay": 1.0,
    "connection_pool_size": 10,
    "enable_logging": True,
    "log_level": "INFO"
}
```

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口是否被占用
   lsof -i :8000
   
   # 检查环境变量
   python -c "import os; print(os.getenv('GOOGLE_MAPS_API_KEY'))"
   ```

2. **连接超时**
   - 确保所有服务都已启动
   - 检查防火墙设置
   - 增加超时时间配置

3. **工具调用失败**
   - 检查参数类型和必需参数
   - 查看服务日志
   - 验证 API 密钥

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查服务状态
python start_all_services.py
# 查看服务状态面板
```

## 📚 扩展开发

### 添加新的 MCP 服务

1. 在 `src/` 目录创建新服务文件
2. 在 `mcp_services_config.py` 添加配置
3. 更新 `SERVICE_STARTUP_ORDER`
4. 测试服务启动和工具调用

### 自定义 Agent 行为

```python
# 创建自定义 Agent
agent = Agent(
    name="CustomAgent",
    instructions="你的自定义指令",
    mcp_servers=list(client.mcp_servers.values()),
    model=OpenAIChatCompletionsModel(model="gpt-4o", openai_client=openai_client)
)
```

## 📄 许可证

本项目采用 MIT 许可证。
