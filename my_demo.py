import dotenv
import asyncio

from agents.agent import Agent
from agents.mcp import MCPServerStreamableHttp
from agents.models.openai_chatcompletions import OpenAIChatCompletionsModel
from agents.run import Runner
from openai import AsyncAzureOpenAI

dotenv.load_dotenv()

async def main():
    # 初始化Azure OpenAI客户端（注意不要传入不必要的api_version）
    azure_client = AsyncAzureOpenAI(
        api_key='8RcRzxMR4UsUAX4yEnXQF9Zl2BVxKEm1bWsWyPnc2SE7E4QDLtw4JQQJ99BGACLArgHXJ3w3AAABACOGclZZ',
        azure_endpoint='https://singoocloud-test.openai.azure.com',
        api_version="2024-12-01-preview"
    )

    model_name = "gpt-4o-2024-08-06"

    # MCP服务器实例，url根据实际部署调整
    mcp_server = MCPServerStreamableHttp(params={"url": "http://127.0.0.1:8000/mcp"})

    await mcp_server.connect()

    try:
        # 创建Agent
        agent = Agent(
            name="GoogleMapAgent",
            instructions=(
                "你是一个专业的推销员，你能根据用户地址给出相应的建议，并且发放优惠券"
            ),
            mcp_servers=[mcp_server],
            model=OpenAIChatCompletionsModel(model=model_name, openai_client=azure_client),
        )

        # 打印可用工具列表，确认连接正常
        tools = await mcp_server.list_tools()
        print("可用工具列表:")
        for t in tools:
            print(t)

        # 测试直调用模型（确认OpenAI客户端正常）
        test_resp = await azure_client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": "Hello"}],
        )
        print("模型测试响应:", test_resp)

        # 运行Agent
        result = await Runner.run(
            starting_agent=agent,
            input="我在Floridusgasse 34, 1210 Wien, Austria，经纬度是多少",
            context="",
            max_turns=10,
        )
        print("Agent回复:", result.final_output)

    finally:
        await mcp_server.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
