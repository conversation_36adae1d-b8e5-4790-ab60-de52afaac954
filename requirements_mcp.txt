# MCP 多服务调用框架依赖 - 适配 Python 3.11
# 核心 MCP 依赖
mcp[cli]>=1.12.4

# OpenAI Agents 框架
openai-agents>=0.2.5
openai>=1.99.6

# 异步支持
aiohttp>=3.9.0
anyio>=4.0.0

# 数据库支持
pymysql>=1.1.1
pymongo>=4.14.0

# 工具库
requests>=2.31.0
python-dotenv>=1.0.0
pydantic>=2.5.0

# 表情符号支持
emoji>=2.14.1

# 日志和调试
loguru>=0.7.2

# 类型检查 (开发时使用，可选)
# mypy>=1.8.0
# types-requests>=2.31.0

# 测试框架 (可选)
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
