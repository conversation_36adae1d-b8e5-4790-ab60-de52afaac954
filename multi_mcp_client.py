"""
步骤3-6: MCP 多服务客户端实现
- 准备 MCP 客户端
- 初始化客户端并获取工具列表  
- 调用具体工具
- 处理返回结果
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional, Union
from contextlib import asynccontextmanager

from agents.agent import Agent
from agents.mcp import MCPServerStreamableHttp, MCPServerStreamableHttpParams
from agents.models.openai_chatcompletions import OpenAIChatCompletionsModel
from agents.run import Runner
# from agents.agent import Agent, Runner, OpenAIChatCompletionsModel
from openai import AsyncAzureOpenAI

from local_logger import logger
from mcp_services_config import (
    MCP_SERVICES_CONFIG,
    get_active_services, 
    get_service_config,
    CLIENT_CONFIG,
    OPENAI_CONFIG
)



class MultiMCPClient:
    """多 MCP 服务客户端"""
    
    def __init__(self):
        self.mcp_servers: Dict[str, MCPServerStreamableHttp] = {}
        self.tools_registry: Dict[str, str] = {}  # tool_name -> service_name
        self.openai_client: Optional[AsyncAzureOpenAI] = None
        self.agent: Optional[Agent] = None
        
    async def initialize(self) -> bool:
        """步骤4: 初始化客户端并获取服务提供的工具列表"""
        try:
            logger.info("🔄 Initializing Multi-MCP Client...")
            
            # 初始化 OpenAI 客户端
            self.openai_client = AsyncAzureOpenAI(
                api_key=OPENAI_CONFIG["api_key"],
                azure_endpoint=OPENAI_CONFIG["azure_endpoint"], 
                api_version=OPENAI_CONFIG["api_version"]
            )
            
            # 初始化所有 MCP 服务连接
            active_services = get_active_services()
            logger.info(f"📋 Found {len(active_services)} active services: {active_services}")
            
            for service_name in active_services:
                if await self._connect_service(service_name):
                    await self._register_tools(service_name)
            
            if not self.mcp_servers:
                logger.error("❌ No MCP services connected")
                return False
            
            # 创建 Agent
            self.agent = Agent(
                name="MultiMCPAgent",
                instructions=(
                    "你是一个专业的多功能助手，可以调用多个 MCP 服务来完成任务。"
                    "你可以进行地址解析、查找附近商店、生成优惠券等操作。"
                    "请根据用户需求选择合适的工具来完成任务。"
                ),
                mcp_servers=list(self.mcp_servers.values()),
                model=OpenAIChatCompletionsModel(
                    model=OPENAI_CONFIG["model"], 
                    openai_client=self.openai_client
                )
            )
            
            logger.info("✅ Multi-MCP Client initialized successfully")
            self._print_available_tools()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize client: {e}")
            return False
    
    async def _connect_service(self, service_name: str) -> bool:
        """连接单个 MCP 服务"""
        try:
            config = get_service_config(service_name)
            logger.info(f"🔗 Connecting to service: {service_name} ({config['url']})")
            
            # 创建 MCP 服务参数
            params = MCPServerStreamableHttpParams(
                name=config["name"],
                url=config["url"],
                timeout=config.get("timeout", 30),
                retries=config.get("retries", 3),
                logLevel=config.get("log_level", "info"),
                disabled=config.get("disabled", False)
            )
            
            # 创建 MCP 服务实例
            mcp_server = MCPServerStreamableHttp(
                params=params,
                cache_tools_list=True,
                client_session_timeout_seconds=CLIENT_CONFIG.get("timeout", 60)
            )
            
            # 连接服务
            await mcp_server.connect()
            self.mcp_servers[service_name] = mcp_server
            
            logger.info(f"✅ Connected to service: {service_name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to service '{service_name}': {e}")
            return False
    
    async def _register_tools(self, service_name: str) -> None:
        """注册服务提供的工具"""
        try:
            mcp_server = self.mcp_servers[service_name]
            tools = await mcp_server.list_tools()
            
            logger.info(f"📝 Registering tools for service '{service_name}':")
            for tool in tools:
                tool_name = tool.name
                self.tools_registry[tool_name] = service_name
                logger.info(f"   - {tool_name}: {tool.description}")
                
        except Exception as e:
            logger.error(f"❌ Failed to register tools for service '{service_name}': {e}")
    
    def _print_available_tools(self):
        """打印所有可用工具"""
        logger.info("🛠️  Available Tools Registry:")
        logger.info("-" * 60)
        
        for tool_name, service_name in self.tools_registry.items():
            logger.info(f"可用工具【{tool_name:20}】 -> {service_name}")
        
        logger.info("-" * 60)
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """步骤5: 调用具体工具"""
        try:
            if tool_name not in self.tools_registry:
                raise ValueError(f"mcp服务 '{tool_name}' not found in registry")
            
            service_name = self.tools_registry[tool_name]
            mcp_server = self.mcp_servers[service_name]
            
            logger.info(f"🔧 调用mcp服务 '{tool_name}' on service '{service_name}'")
            logger.info(f"   Parameters: {kwargs}")
            
            # 调用工具
            result = await mcp_server.call_tool(tool_name, kwargs)
            
            logger.info(f"✅ 服务 '{tool_name}' 执行成功")
            return result
            
        except Exception as e:
            logger.error(f"❌ Failed to call tool '{tool_name}': {e}")
            raise
    
    async def run_agent(self, user_input: str, context: str = "", max_turns: int = 10) -> str:
        """使用 Agent 处理用户输入"""
        try:
            if not self.agent:
                raise ValueError("Agent not initialized")
            
            logger.info(f"🤖 Running agent with input: {user_input}")
            
            result = await Runner.run(
                starting_agent=self.agent,
                input=user_input,
                context=context,
                max_turns=max_turns
            )
            
            logger.info("✅ Agent execution completed")
            return result.final_output
            
        except Exception as e:
            logger.error(f"❌ Agent execution failed: {e}")
            raise
    
    def process_result(self, result: Any) -> Dict[str, Any]:
        """步骤6: 处理返回结果"""
        try:
            # 标准化结果格式
            if isinstance(result, str):
                try:
                    # 尝试解析 JSON 字符串
                    parsed_result = json.loads(result)
                    return {
                        "success": True,
                        "data": parsed_result,
                        "raw_result": result,
                        "type": "json"
                    }
                except json.JSONDecodeError:
                    return {
                        "success": True,
                        "data": result,
                        "raw_result": result,
                        "type": "string"
                    }
            
            elif isinstance(result, dict):
                return {
                    "success": True,
                    "data": result,
                    "raw_result": result,
                    "type": "dict"
                }
            
            else:
                return {
                    "success": True,
                    "data": result,
                    "raw_result": result,
                    "type": type(result).__name__
                }
                
        except Exception as e:
            logger.error(f"❌ Failed to process result: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_result": result,
                "type": "error"
            }
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 Cleaning up MCP client...")
        
        for service_name, mcp_server in self.mcp_servers.items():
            try:
                await mcp_server.cleanup()
                logger.info(f"✅ 清理工具: 【{service_name}】")
            except Exception as e:
                logger.error(f"❌ Failed to cleanup service '{service_name}': {e}")
        
        self.mcp_servers.clear()
        self.tools_registry.clear()
        logger.info("✅ Cleanup completed")
    
    @asynccontextmanager
    async def managed_client(self):
        """上下文管理器，自动清理资源"""
        try:
            if await self.initialize():
                yield self
            else:
                raise RuntimeError("Failed to initialize MCP client")
        finally:
            await self.cleanup()

# 便捷函数
async def create_mcp_client() -> MultiMCPClient:
    """创建并初始化 MCP 客户端"""
    client = MultiMCPClient()
    if await client.initialize():
        return client
    else:
        raise RuntimeError("Failed to create MCP client")

async def quick_call(tool_name: str, **kwargs) -> Any:
    """快速调用工具的便捷函数"""
    async with MultiMCPClient().managed_client() as client:
        return await client.call_tool(tool_name, **kwargs)

async def quick_agent_run(user_input: str, context: str = "") -> str:
    """快速运行 Agent 的便捷函数"""
    async with MultiMCPClient().managed_client() as client:
        return await client.run_agent(user_input, context)
