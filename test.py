import re
from contextlib import asynccontextmanager

import dotenv
from agents import Agent, <PERSON>, AsyncOpenAI, OpenAIChatCompletionsModel, set_default_openai_client
from agents.mcp import MCPServerStreamableHttp
import asyncio
import json
import emoji
from agents.run import <PERSON><PERSON><PERSON><PERSON>
from openai import AsyncAzureOpenAI
from pydantic import BaseModel
from openai.types.responses import ResponseTextDeltaEvent

dotenv.load_dotenv()

google_mcp_server = {
    "name": "GoogleMapsMCPServer",
    # sse服务器的地址
    "url": "http://127.0.0.1:8000/mcp",
    "timeout": 30,
    "retries": 3,
    "logLevel": "info",
    "disabled": False
}

coupon_mcp_server = {
    "name": "CouponMCPServer",
    # sse服务器的地址
    "url": "http://127.0.0.1:8001/mcp",
    "timeout": 30,
    "retries": 3,
    "logLevel": "info",
    "disabled": False
}

#################### 
# https://api.deepseek.com
# custom_client = AsyncOpenAI(base_url="https://api.deepseek.com", api_key="sk-********************************")
# # 设置默认的OpenAI客户端
# set_default_openai_client(custom_client)
# # deepseek-chat
# model_name = 'deepseek-chat'

async def main2():
    custom_client = AsyncAzureOpenAI(
        api_key='8RcRzxMR4UsUAX4yEnXQF9Zl2BVxKEm1bWsWyPnc2SE7E4QDLtw4JQQJ99BGACLArgHXJ3w3AAABACOGclZZ',
        azure_endpoint='https://singoocloud-test.openai.azure.com',
        api_version="2024-12-01-preview"
    )

    model_name = 'gpt-4o-2024-08-06'
    mcp_server = MCPServerStreamableHttp(
        params={"url": "http://127.0.0.1:8000/mcp", }
    )
    try:
        await mcp_server.connect()

        minio_agent = Agent(
            name="GoogleMapAgent",
            instructions="你是一个专业的推销员，你能根据用户地址给出相应的建议，并且发放优惠券",
            mcp_servers=[mcp_server],
            model=OpenAIChatCompletionsModel(model=model_name, openai_client=custom_client)
        )

        # 验证连接
        tools = await mcp_server.list_tools()
        print("可用工具列表:")
        for tool in tools:
            print(tool)

        resp = await custom_client.chat.completions.create(
            model=model_name,  # 这里是部署名称
            messages=[{"role": "user", "content": "Hello"}]
        )
        print(resp)


        result = await Runner.run(
            starting_agent=minio_agent,
            input="我在Floridusgasse 34, 1210 Wien, Austria，经纬度是多少",
            context="",
            max_turns=10
        )
        print(f"Agent Response: {result.final_output}")
    finally:
        await mcp_server.cleanup()


if __name__ == "__main__":
    # 使用anyio运行以避免取消作用域问题
    asyncio.run(main2())
