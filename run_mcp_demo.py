#!/usr/bin/env python3
"""
MCP 框架一键演示脚本
自动启动服务、运行测试、执行示例
"""

import asyncio
import subprocess
import sys
import time
import signal
import os
from typing import Optional

class MCPDemoRunner:
    """MCP 演示运行器"""
    
    def __init__(self):
        self.services_process: Optional[subprocess.Popen] = None
        self.running = True
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n🔔 收到信号 {signum}，正在关闭...")
        self.running = False
        self.cleanup()
        sys.exit(0)
    
    def cleanup(self):
        """清理资源"""
        if self.services_process:
            print("🛑 正在停止 MCP 服务...")
            self.services_process.terminate()
            try:
                self.services_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                print("⚠️  强制终止服务进程...")
                self.services_process.kill()
                self.services_process.wait()
            print("✅ 服务已停止")
    
    def check_dependencies(self) -> bool:
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查 Python 版本
        if sys.version_info < (3, 11):
            print(f"❌ 需要 Python 3.11+，当前版本: {sys.version}")
            return False
        
        # 检查必需的包
        required_packages = [
            "mcp", "openai", "agents", "requests", "dotenv", "pymysql"
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少依赖包: {missing_packages}")
            print("请运行: pip install -r requirements_mcp.txt")
            return False
        
        print("✅ 依赖检查通过")
        return True
    
    def check_env_vars(self) -> bool:
        """检查环境变量"""
        print("🔍 检查环境变量...")
        
        required_vars = [
            "GOOGLE_MAPS_API_KEY",
            "AZURE_OPENAI_API_KEY", 
            "AZURE_OPENAI_ENDPOINT"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️  缺少环境变量: {missing_vars}")
            print("请在 .env 文件中配置这些变量")
            return False
        
        print("✅ 环境变量检查通过")
        return True
    
    def start_services(self) -> bool:
        """启动 MCP 服务"""
        print("🚀 启动 MCP 服务...")
        
        try:
            self.services_process = subprocess.Popen(
                [sys.executable, "start_all_services.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待服务启动
            print("⏳ 等待服务启动...")
            start_time = time.time()
            timeout = 60  # 60秒超时
            
            while time.time() - start_time < timeout:
                if self.services_process.poll() is not None:
                    print("❌ 服务进程意外退出")
                    return False
                
                # 检查服务是否就绪（简单的端口检查）
                try:
                    import requests
                    response = requests.get("http://127.0.0.1:8000/mcp", timeout=2)
                    if response.status_code == 200:
                        print("✅ 服务启动成功")
                        return True
                except:
                    pass
                
                time.sleep(2)
                print(".", end="", flush=True)
            
            print(f"\n⏰ 服务启动超时 ({timeout}秒)")
            return False
            
        except Exception as e:
            print(f"❌ 启动服务失败: {e}")
            return False
    
    async def run_tests(self) -> bool:
        """运行测试"""
        print("\n🧪 运行框架测试...")
        
        try:
            process = await asyncio.create_subprocess_exec(
                sys.executable, "test_mcp_framework.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT
            )
            
            stdout, _ = await process.communicate()
            
            if stdout:
                print(stdout.decode())
            
            return process.returncode == 0
            
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            return False
    
    async def run_examples(self) -> bool:
        """运行示例"""
        print("\n🎯 运行使用示例...")
        
        try:
            process = await asyncio.create_subprocess_exec(
                sys.executable, "example_usage.py",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.STDOUT
            )
            
            stdout, _ = await process.communicate()
            
            if stdout:
                print(stdout.decode())
            
            return process.returncode == 0
            
        except Exception as e:
            print(f"❌ 示例执行失败: {e}")
            return False
    
    async def interactive_mode(self):
        """交互模式"""
        print("\n🎮 进入交互模式")
        print("你可以输入问题，Agent 会使用 MCP 服务来回答")
        print("输入 'quit' 或 'exit' 退出")
        print("-" * 50)
        
        try:
            from multi_mcp_client import MultiMCPClient
            
            async with MultiMCPClient().managed_client() as client:
                while self.running:
                    try:
                        user_input = input("\n👤 你: ").strip()
                        
                        if user_input.lower() in ['quit', 'exit', '退出']:
                            break
                        
                        if not user_input:
                            continue
                        
                        print("🤖 Agent 正在思考...")
                        response = await client.run_agent(user_input, max_turns=5)
                        print(f"🤖 Agent: {response}")
                        
                    except KeyboardInterrupt:
                        break
                    except Exception as e:
                        print(f"❌ 处理输入时出错: {e}")
                        
        except Exception as e:
            print(f"❌ 交互模式启动失败: {e}")
    
    async def run_demo(self):
        """运行完整演示"""
        print("🎉 MCP 多服务调用框架演示")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 1. 检查依赖
            if not self.check_dependencies():
                return False
            
            # 2. 检查环境变量
            if not self.check_env_vars():
                print("💡 提示: 你可以继续运行，但某些功能可能不可用")
                response = input("是否继续? (y/N): ").strip().lower()
                if response != 'y':
                    return False
            
            # 3. 启动服务
            if not self.start_services():
                return False
            
            # 4. 运行测试
            test_success = await self.run_tests()
            if not test_success:
                print("⚠️  测试失败，但可以继续运行示例")
            
            # 5. 运行示例
            example_success = await self.run_examples()
            if not example_success:
                print("⚠️  示例运行失败")
            
            # 6. 交互模式
            if test_success or example_success:
                response = input("\n是否进入交互模式? (Y/n): ").strip().lower()
                if response != 'n':
                    await self.interactive_mode()
            
            print("\n🎉 演示完成!")
            return True
            
        except KeyboardInterrupt:
            print("\n🔔 演示被用户中断")
            return False
        except Exception as e:
            print(f"\n❌ 演示过程中发生错误: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    runner = MCPDemoRunner()
    
    try:
        success = asyncio.run(runner.run_demo())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 再见!")
        sys.exit(0)

if __name__ == "__main__":
    main()
