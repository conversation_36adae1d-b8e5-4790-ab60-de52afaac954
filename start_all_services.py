"""
步骤2: 逐一启动所有 MCP 服务
支持本地可执行文件、HTTP 和 SSE 方式启动服务
"""

import asyncio
import subprocess
import time
import signal
import sys
import os
import requests
from typing import List, Dict, Optional
from mcp_services_config import (
    MCP_SERVICES_CONFIG, 
    SERVICE_STARTUP_ORDER, 
    get_active_services,
    get_service_config
)

class MCPServiceManager:
    def __init__(self):
        self.running_processes: Dict[str, subprocess.Popen] = {}
        self.service_status: Dict[str, str] = {}
        
    def start_service(self, service_name: str) -> bool:
        """启动单个 MCP 服务"""
        try:
            config = get_service_config(service_name)
            
            if config.get("disabled", False):
                print(f"⏭️  Service '{service_name}' is disabled, skipping...")
                return True
                
            print(f"🚀 Starting service: {service_name}")
            print(f"   Command: {config['startup_command']}")
            print(f"   URL: {config['url']}")
            
            # 设置环境变量
            env = os.environ.copy()
            env.update(config.get("env_vars", {}))
            
            # 启动进程
            process = subprocess.Popen(
                config["startup_command"].split(),
                cwd=config.get("working_directory", "."),
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.running_processes[service_name] = process
            self.service_status[service_name] = "starting"
            
            # 等待服务启动
            if self.wait_for_service_ready(service_name, timeout=30):
                self.service_status[service_name] = "running"
                print(f"✅ Service '{service_name}' started successfully")
                return True
            else:
                self.service_status[service_name] = "failed"
                print(f"❌ Service '{service_name}' failed to start")
                return False
                
        except Exception as e:
            print(f"❌ Error starting service '{service_name}': {e}")
            self.service_status[service_name] = "error"
            return False
    
    def wait_for_service_ready(self, service_name: str, timeout: int = 30) -> bool:
        """等待服务就绪"""
        config = get_service_config(service_name)
        url = config["url"]
        
        print(f"⏳ Waiting for service '{service_name}' to be ready...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                # 检查进程是否还在运行
                process = self.running_processes.get(service_name)
                if process and process.poll() is not None:
                    print(f"❌ Process for '{service_name}' has terminated")
                    return False
                
                # 尝试连接服务
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    return True
                    
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            print(".", end="", flush=True)
        
        print(f"\n⏰ Timeout waiting for service '{service_name}'")
        return False
    
    def start_all_services(self) -> bool:
        """按顺序启动所有服务"""
        print("🔄 Starting all MCP services...")
        
        active_services = get_active_services()
        if not active_services:
            print("⚠️  No active services found")
            return True
        
        success_count = 0
        for service_name in SERVICE_STARTUP_ORDER:
            if service_name in active_services:
                if self.start_service(service_name):
                    success_count += 1
                    time.sleep(2)  # 服务间启动间隔
                else:
                    print(f"⚠️  Failed to start service '{service_name}', continuing...")
        
        print(f"\n📊 Service startup summary: {success_count}/{len(active_services)} services started")
        return success_count > 0
    
    def stop_service(self, service_name: str) -> bool:
        """停止单个服务"""
        try:
            process = self.running_processes.get(service_name)
            if process:
                print(f"🛑 Stopping service: {service_name}")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    print(f"⚠️  Force killing service: {service_name}")
                    process.kill()
                    process.wait()
                
                del self.running_processes[service_name]
                self.service_status[service_name] = "stopped"
                print(f"✅ Service '{service_name}' stopped")
                return True
            else:
                print(f"⚠️  Service '{service_name}' is not running")
                return False
                
        except Exception as e:
            print(f"❌ Error stopping service '{service_name}': {e}")
            return False
    
    def stop_all_services(self):
        """停止所有服务"""
        print("🛑 Stopping all MCP services...")
        
        for service_name in list(self.running_processes.keys()):
            self.stop_service(service_name)
        
        print("✅ All services stopped")
    
    def get_service_status(self) -> Dict[str, str]:
        """获取所有服务状态"""
        # 更新进程状态
        for service_name, process in list(self.running_processes.items()):
            if process.poll() is not None:
                self.service_status[service_name] = "stopped"
                del self.running_processes[service_name]
        
        return self.service_status.copy()
    
    def print_status(self):
        """打印服务状态"""
        status = self.get_service_status()
        print("\n📋 Service Status:")
        print("-" * 50)
        
        for service_name in get_active_services():
            service_status = status.get(service_name, "not_started")
            config = get_service_config(service_name)
            
            status_emoji = {
                "running": "🟢",
                "starting": "🟡", 
                "stopped": "🔴",
                "failed": "❌",
                "error": "💥",
                "not_started": "⚪"
            }.get(service_status, "❓")
            
            print(f"{status_emoji} {service_name:15} | {service_status:10} | {config['url']}")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🔔 Received signal {signum}, shutting down...")
    if hasattr(signal_handler, 'manager'):
        signal_handler.manager.stop_all_services()
    sys.exit(0)

def main():
    """主函数"""
    manager = MCPServiceManager()
    signal_handler.manager = manager
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # 启动所有服务
        if manager.start_all_services():
            print("\n🎉 MCP services started successfully!")
            manager.print_status()
            
            print("\n💡 Services are running. Press Ctrl+C to stop all services.")
            print("   You can now run your MCP client to connect to these services.")
            
            # 保持运行
            while True:
                time.sleep(10)
                # 定期检查服务状态
                status = manager.get_service_status()
                failed_services = [name for name, stat in status.items() if stat in ["stopped", "failed", "error"]]
                if failed_services:
                    print(f"⚠️  Detected failed services: {failed_services}")
        else:
            print("❌ Failed to start MCP services")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🔔 Received interrupt signal")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
    finally:
        manager.stop_all_services()

if __name__ == "__main__":
    main()
