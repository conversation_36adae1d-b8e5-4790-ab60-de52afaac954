import asyncio

from local_logger import logger
from multi_mcp_client import MultiMCPClient


async def example():
    async with MultiMCPClient().managed_client() as client:
        # 地理编码
        result = await client.call_tool(
            "geocode",
            address="我在Floridusgasse 34, 1210 Wien, Austria，经纬度是多少"
        )
        logger.info(f"我在Floridusgasse 34, 1210 Wien, Austria，经纬度是多少")
        logger.info(f"经纬度: {result}")

        # 生成优惠券
        coupon = await client.call_tool(
            "generate_coupon",
            company_id=1,
            country_code="CN",
            channel="website",
            contact_type="mobile",
            contact_value="+86138000000"
        )
        logger.info(f"优惠券: {coupon}")


asyncio.run(example())