"""
MCP 服务配置文件
定义所有 MCP 服务的连接信息和启动参数
"""

import os
from typing import Dict, List, Any

# 步骤1: 确认每个 MCP 服务的启动方式
MCP_SERVICES_CONFIG = {
    # Google Maps MCP 服务
    "google_maps": {
        "name": "GoogleMapsMCPServer",
        "type": "http",  # stdio, http, sse
        "url": "http://127.0.0.1:8000/mcp",
        "host": "127.0.0.1",
        "port": 8000,
        "timeout": 30,
        "retries": 3,
        "log_level": "info",
        "disabled": False,
        "startup_command": "python src/google_map.py",
        "working_directory": ".",
        "env_vars": {
            "GOOGLE_MAPS_API_KEY": os.getenv("GOOGLE_MAPS_API_KEY", "your_google_maps_api_key")
        },
        "description": "Google Maps geocoding and nearby stores service"
    },
    
    # 优惠券 MCP 服务
    "coupon": {
        "name": "CouponMCPServer", 
        "type": "http",
        "url": "http://127.0.0.1:8001/mcp",
        "host": "127.0.0.1",
        "port": 8001,
        "timeout": 30,
        "retries": 3,
        "log_level": "info",
        "disabled": False,
        "startup_command": "python src/coupon_mcp.py",
        "working_directory": ".",
        "env_vars": {
            "SECRET_KEY": os.getenv("SECRET_KEY", "your_secret_key"),
            "MYSQL_HOST": os.getenv("MYSQL_HOST", "localhost"),
            "MYSQL_PORT": os.getenv("MYSQL_PORT", "3306"),
            "MYSQL_USER": os.getenv("MYSQL_USER", "root"),
            "MYSQL_PASSWORD": os.getenv("MYSQL_PASSWORD", "password"),
            "MYSQL_DATABASE": os.getenv("MYSQL_DATABASE", "coupon_db")
        },
        "description": "Coupon generation and management service"
    },
    
    # RAG 知识库服务
    "rag": {
        "name": "RAGMCPServer",
        "type": "http",
        "url": "http://127.0.0.1:8002/mcp",
        "host": "127.0.0.1",
        "port": 8002,
        "timeout": 30,
        "retries": 3,
        "log_level": "info",
        "disabled": False,  # 启用 RAG 服务
        "startup_command": "python src/rag_mcp.py",
        "working_directory": ".",
        "env_vars": {
            "RAG_URL": os.getenv("RAG_URL", "http://*************:5008/")
        },
        "description": "RAG knowledge base search service"
    }
}

# 步骤2: 服务启动顺序配置
SERVICE_STARTUP_ORDER = [
    "google_maps",
    "coupon", 
    "rag"
]

# 步骤3: 客户端配置
CLIENT_CONFIG = {
    "timeout": 60,
    "max_retries": 3,
    "retry_delay": 1.0,
    "connection_pool_size": 10,
    "enable_logging": True,
    "log_level": "INFO"
}

# OpenAI 客户端配置
OPENAI_CONFIG = {
    "api_key": os.getenv("AZURE_OPENAI_API_KEY", "your_api_key"),
    "azure_endpoint": os.getenv("AZURE_OPENAI_ENDPOINT", "https://your-endpoint.openai.azure.com"),
    "api_version": "2024-12-01-preview",
    "model": os.getenv("AZURE_OPENAI_MODEL", "gpt-4o")
}

def get_active_services() -> List[str]:
    """获取所有启用的服务列表"""
    return [
        service_name for service_name, config in MCP_SERVICES_CONFIG.items()
        if not config.get("disabled", False)
    ]

def get_service_config(service_name: str) -> Dict[str, Any]:
    """获取指定服务的配置"""
    if service_name not in MCP_SERVICES_CONFIG:
        raise ValueError(f"Service '{service_name}' not found in configuration")
    return MCP_SERVICES_CONFIG[service_name]

def get_service_url(service_name: str) -> str:
    """获取服务的完整URL"""
    config = get_service_config(service_name)
    return config["url"]

def get_service_startup_command(service_name: str) -> str:
    """获取服务的启动命令"""
    config = get_service_config(service_name)
    return config["startup_command"]

def validate_config() -> bool:
    """验证配置的有效性"""
    try:
        for service_name, config in MCP_SERVICES_CONFIG.items():
            required_fields = ["name", "type", "url", "host", "port"]
            for field in required_fields:
                if field not in config:
                    print(f"Missing required field '{field}' in service '{service_name}'")
                    return False
        return True
    except Exception as e:
        print(f"Configuration validation error: {e}")
        return False

if __name__ == "__main__":
    # 配置验证
    if validate_config():
        print("✅ Configuration validation passed")
        print(f"Active services: {get_active_services()}")
        for service in get_active_services():
            config = get_service_config(service)
            print(f"  - {service}: {config['url']} ({config['description']})")
    else:
        print("❌ Configuration validation failed")
