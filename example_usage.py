"""
多 MCP 服务调用示例
演示如何使用 MultiMCPClient 调用多个 MCP 服务
"""

import asyncio
import json
from multi_mcp_client import MultiMCPClient, quick_agent_run, quick_call

async def example_1_direct_tool_calls():
    """示例1: 直接调用工具"""
    print("=" * 60)
    print("示例1: 直接调用 MCP 工具")
    print("=" * 60)
    
    async with MultiMCPClient().managed_client() as client:
        try:
            # 调用地理编码工具
            print("\n🌍 调用地理编码工具...")
            geocode_result = await client.call_tool(
                "geocode", 
                address="Floridusgasse 34, 1210 Wien, Austria"
            )
            processed_result = client.process_result(geocode_result)
            print(f"地理编码结果: {json.dumps(processed_result, indent=2, ensure_ascii=False)}")
            
            # 调用附近商店查询工具
            print("\n🏪 调用附近商店查询工具...")
            stores_result = await client.call_tool(
                "get_nearby_stores",
                address="Floridusgasse 34, 1210 Wien, Austria"
            )
            processed_result = client.process_result(stores_result)
            print(f"附近商店结果: {json.dumps(processed_result, indent=2, ensure_ascii=False)}")
            
            # 调用优惠券生成工具
            print("\n🎫 调用优惠券生成工具...")
            coupon_result = await client.call_tool(
                "generate_coupon",
                company_id=1,
                country_code="AT",
                channel="website",
                contact_type="mobile",
                contact_value="+43123456789"
            )
            processed_result = client.process_result(coupon_result)
            print(f"优惠券生成结果: {json.dumps(processed_result, indent=2, ensure_ascii=False)}")
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")

async def example_2_agent_conversation():
    """示例2: 使用 Agent 进行对话"""
    print("\n" + "=" * 60)
    print("示例2: 使用 Agent 进行智能对话")
    print("=" * 60)
    
    async with MultiMCPClient().managed_client() as client:
        try:
            # 场景1: 地址查询和商店推荐
            print("\n🤖 场景1: 地址查询和商店推荐")
            user_input_1 = "我在维也纳的 Floridusgasse 34, 1210 Wien, Austria，请帮我找到附近的商店"
            response_1 = await client.run_agent(user_input_1)
            print(f"Agent 回复: {response_1}")
            
            # 场景2: 优惠券生成
            print("\n🤖 场景2: 优惠券生成")
            user_input_2 = "我是奥地利用户，手机号是+43123456789，请为我生成一张优惠券，我是通过网站渠道来的"
            response_2 = await client.run_agent(user_input_2)
            print(f"Agent 回复: {response_2}")
            
            # 场景3: 综合服务
            print("\n🤖 场景3: 综合服务")
            user_input_3 = (
                "我在维也纳市中心，地址是 Stephansplatz 1, 1010 Wien, Austria。"
                "请帮我找到最近的3家商店，然后为我生成一张优惠券。"
                "我的联系方式是 +43987654321，通过移动应用访问。"
            )
            response_3 = await client.run_agent(user_input_3)
            print(f"Agent 回复: {response_3}")
            
        except Exception as e:
            print(f"❌ Agent 对话失败: {e}")

async def example_3_quick_functions():
    """示例3: 使用便捷函数"""
    print("\n" + "=" * 60)
    print("示例3: 使用便捷函数")
    print("=" * 60)
    
    try:
        # 快速工具调用
        print("\n⚡ 快速地理编码...")
        result = await quick_call(
            "geocode",
            address="北京市天安门广场"
        )
        print(f"结果: {result}")
        
        # 快速 Agent 运行
        print("\n⚡ 快速 Agent 对话...")
        response = await quick_agent_run(
            "请帮我查询北京天安门广场的经纬度坐标"
        )
        print(f"Agent 回复: {response}")
        
    except Exception as e:
        print(f"❌ 便捷函数调用失败: {e}")

async def example_4_error_handling():
    """示例4: 错误处理"""
    print("\n" + "=" * 60)
    print("示例4: 错误处理演示")
    print("=" * 60)
    
    async with MultiMCPClient().managed_client() as client:
        # 调用不存在的工具
        print("\n❌ 调用不存在的工具...")
        try:
            await client.call_tool("non_existent_tool", param="test")
        except Exception as e:
            print(f"预期错误: {e}")
        
        # 传递错误参数
        print("\n❌ 传递错误参数...")
        try:
            await client.call_tool("geocode")  # 缺少必需参数
        except Exception as e:
            print(f"预期错误: {e}")
        
        # 处理异常结果
        print("\n🔧 处理各种类型的结果...")
        test_results = [
            '{"status": "success", "data": {"lat": 39.9042, "lng": 116.4074}}',
            {"error": "Service unavailable"},
            "Simple string result",
            None,
            123
        ]
        
        for i, test_result in enumerate(test_results):
            processed = client.process_result(test_result)
            print(f"结果 {i+1}: {processed}")

async def example_5_batch_operations():
    """示例5: 批量操作"""
    print("\n" + "=" * 60)
    print("示例5: 批量操作演示")
    print("=" * 60)
    
    async with MultiMCPClient().managed_client() as client:
        # 批量地理编码
        addresses = [
            "北京市天安门广场",
            "上海市外滩",
            "广州市珠江新城",
            "深圳市南山区"
        ]
        
        print("\n🌍 批量地理编码...")
        geocode_tasks = [
            client.call_tool("geocode", address=addr) 
            for addr in addresses
        ]
        
        try:
            results = await asyncio.gather(*geocode_tasks, return_exceptions=True)
            
            for i, (addr, result) in enumerate(zip(addresses, results)):
                if isinstance(result, Exception):
                    print(f"地址 {i+1} ({addr}): 错误 - {result}")
                else:
                    processed = client.process_result(result)
                    print(f"地址 {i+1} ({addr}): {processed['data']}")
                    
        except Exception as e:
            print(f"❌ 批量操作失败: {e}")

async def main():
    """主函数 - 运行所有示例"""
    print("🚀 多 MCP 服务调用示例")
    print("请确保已经启动了所有 MCP 服务 (运行 python start_all_services.py)")
    print()
    
    # 运行所有示例
    examples = [
        example_1_direct_tool_calls,
        example_2_agent_conversation, 
        example_3_quick_functions,
        example_4_error_handling,
        example_5_batch_operations
    ]
    
    for example_func in examples:
        try:
            await example_func()
            print("\n" + "✅ 示例完成" + "\n")
            await asyncio.sleep(1)  # 短暂暂停
        except Exception as e:
            print(f"\n❌ 示例执行失败: {e}\n")
    
    print("🎉 所有示例执行完成!")

if __name__ == "__main__":
    asyncio.run(main())
