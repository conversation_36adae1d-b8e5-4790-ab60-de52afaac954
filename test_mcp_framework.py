"""
MCP 框架测试脚本
用于验证多 MCP 服务调用框架的功能
"""

import asyncio
import json
import sys
import time
from typing import List, Dict, Any

from mcp_services_config import validate_config, get_active_services
from multi_mcp_client import MultiMCPClient

class MCPFrameworkTester:
    """MCP 框架测试器"""
    
    def __init__(self):
        self.test_results: List[Dict[str, Any]] = []
        self.client: Optional[MultiMCPClient] = None
    
    def log_test_result(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "data": data,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        if data and not success:
            print(f"   详细信息: {data}")
    
    async def test_config_validation(self):
        """测试配置验证"""
        try:
            is_valid = validate_config()
            active_services = get_active_services()
            
            self.log_test_result(
                "配置验证",
                is_valid,
                f"配置有效，发现 {len(active_services)} 个活跃服务",
                {"active_services": active_services}
            )
            return is_valid
        except Exception as e:
            self.log_test_result("配置验证", False, f"配置验证异常: {e}")
            return False
    
    async def test_client_initialization(self):
        """测试客户端初始化"""
        try:
            self.client = MultiMCPClient()
            success = await self.client.initialize()
            
            if success:
                tools_count = len(self.client.tools_registry)
                services_count = len(self.client.mcp_servers)
                
                self.log_test_result(
                    "客户端初始化",
                    True,
                    f"成功连接 {services_count} 个服务，注册 {tools_count} 个工具",
                    {
                        "services": list(self.client.mcp_servers.keys()),
                        "tools": list(self.client.tools_registry.keys())
                    }
                )
            else:
                self.log_test_result("客户端初始化", False, "客户端初始化失败")
            
            return success
        except Exception as e:
            self.log_test_result("客户端初始化", False, f"初始化异常: {e}")
            return False
    
    async def test_tool_calls(self):
        """测试工具调用"""
        if not self.client:
            self.log_test_result("工具调用测试", False, "客户端未初始化")
            return False
        
        test_cases = [
            {
                "name": "地理编码测试",
                "tool": "geocode",
                "params": {"address": "北京市天安门广场"},
                "expected_keys": ["latitude", "longitude"]
            },
            {
                "name": "附近商店测试", 
                "tool": "get_nearby_stores",
                "params": {"address": "北京市天安门广场"},
                "expected_keys": ["result", "data"]
            }
        ]
        
        success_count = 0
        for test_case in test_cases:
            try:
                if test_case["tool"] not in self.client.tools_registry:
                    self.log_test_result(
                        test_case["name"],
                        False,
                        f"工具 '{test_case['tool']}' 未找到"
                    )
                    continue
                
                result = await self.client.call_tool(test_case["tool"], **test_case["params"])
                processed = self.client.process_result(result)
                
                if processed["success"]:
                    # 检查预期的键是否存在
                    data = processed["data"]
                    if isinstance(data, str):
                        try:
                            data = json.loads(data)
                        except:
                            pass
                    
                    has_expected_keys = True
                    if isinstance(data, dict):
                        for key in test_case.get("expected_keys", []):
                            if key not in str(data):
                                has_expected_keys = False
                                break
                    
                    if has_expected_keys:
                        success_count += 1
                        self.log_test_result(
                            test_case["name"],
                            True,
                            "工具调用成功",
                            {"result_type": processed["type"]}
                        )
                    else:
                        self.log_test_result(
                            test_case["name"],
                            False,
                            "返回结果格式不符合预期",
                            {"result": processed}
                        )
                else:
                    self.log_test_result(
                        test_case["name"],
                        False,
                        "工具调用失败",
                        processed
                    )
                    
            except Exception as e:
                self.log_test_result(
                    test_case["name"],
                    False,
                    f"工具调用异常: {e}"
                )
        
        return success_count == len(test_cases)
    
    async def test_agent_conversation(self):
        """测试 Agent 对话"""
        if not self.client or not self.client.agent:
            self.log_test_result("Agent 对话测试", False, "Agent 未初始化")
            return False
        
        test_queries = [
            "请帮我查询北京天安门广场的经纬度",
            "Hello, can you help me?",
        ]
        
        success_count = 0
        for i, query in enumerate(test_queries):
            try:
                response = await self.client.run_agent(query, max_turns=3)
                
                if response and len(response.strip()) > 0:
                    success_count += 1
                    self.log_test_result(
                        f"Agent 对话测试 {i+1}",
                        True,
                        f"获得回复 ({len(response)} 字符)",
                        {"query": query, "response_length": len(response)}
                    )
                else:
                    self.log_test_result(
                        f"Agent 对话测试 {i+1}",
                        False,
                        "Agent 回复为空",
                        {"query": query}
                    )
                    
            except Exception as e:
                self.log_test_result(
                    f"Agent 对话测试 {i+1}",
                    False,
                    f"Agent 对话异常: {e}",
                    {"query": query}
                )
        
        return success_count == len(test_queries)
    
    async def test_error_handling(self):
        """测试错误处理"""
        if not self.client:
            self.log_test_result("错误处理测试", False, "客户端未初始化")
            return False
        
        error_tests = [
            {
                "name": "不存在的工具",
                "action": lambda: self.client.call_tool("non_existent_tool", param="test"),
                "expected_error": True
            },
            {
                "name": "缺少参数",
                "action": lambda: self.client.call_tool("geocode"),  # 缺少 address 参数
                "expected_error": True
            }
        ]
        
        success_count = 0
        for test in error_tests:
            try:
                await test["action"]()
                # 如果没有抛出异常，但我们期望有异常
                if test["expected_error"]:
                    self.log_test_result(
                        test["name"],
                        False,
                        "期望抛出异常但没有"
                    )
                else:
                    success_count += 1
                    self.log_test_result(test["name"], True, "正常执行")
                    
            except Exception as e:
                if test["expected_error"]:
                    success_count += 1
                    self.log_test_result(
                        test["name"],
                        True,
                        f"正确捕获异常: {type(e).__name__}"
                    )
                else:
                    self.log_test_result(
                        test["name"],
                        False,
                        f"意外异常: {e}"
                    )
        
        return success_count == len(error_tests)
    
    async def cleanup(self):
        """清理资源"""
        if self.client:
            await self.client.cleanup()
    
    def print_summary(self):
        """打印测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("🧪 测试摘要")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test_name']}: {result['message']}")
        
        print("=" * 60)
        
        return passed_tests == total_tests

async def main():
    """主测试函数"""
    print("🧪 MCP 框架测试开始")
    print("请确保已经启动了所有 MCP 服务 (python start_all_services.py)")
    print()
    
    tester = MCPFrameworkTester()
    
    try:
        # 运行所有测试
        tests = [
            tester.test_config_validation(),
            tester.test_client_initialization(),
            tester.test_tool_calls(),
            tester.test_agent_conversation(),
            tester.test_error_handling()
        ]
        
        for test in tests:
            await test
            await asyncio.sleep(0.5)  # 短暂暂停
        
        # 打印摘要
        all_passed = tester.print_summary()
        
        if all_passed:
            print("\n🎉 所有测试通过！MCP 框架工作正常。")
            sys.exit(0)
        else:
            print("\n⚠️  部分测试失败，请检查配置和服务状态。")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🔔 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        sys.exit(1)
    finally:
        await tester.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
