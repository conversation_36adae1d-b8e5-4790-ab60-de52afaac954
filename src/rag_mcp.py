"""
RAG 知识库 MCP 服务
将现有的 RAG 功能包装为 MCP 服务
"""

import os
import sys
import json
from typing import Annotated

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mcp.server.fastmcp import FastMCP
from dotenv import load_dotenv
from src.rag import VectorLibrary

load_dotenv()

mcp = FastMCP(
    "RAGMCPServer",
    stateless_http=True,
    host="0.0.0.0",
    port=8002,
    streamable_http_path="/mcp"
)

# 初始化 RAG 库
rag_library = VectorLibrary()

@mcp.tool()
def search_knowledge(
    company_id: Annotated[int, "Company ID for knowledge base search"],
    keywords: Annotated[str, "Search keywords or query text"],
    top_k: Annotated[int, "Number of top results to return"] = 3
) -> str:
    """在知识库中搜索相关信息"""
    try:
        # 调用现有的 RAG 搜索功能
        result = rag_library.search_information(company_id, keywords)
        
        if result:
            return json.dumps({
                "result": True,
                "data": {
                    "content": result,
                    "query": keywords,
                    "company_id": company_id,
                    "top_k": top_k
                },
                "message": "Knowledge search completed successfully"
            }, ensure_ascii=False)
        else:
            return json.dumps({
                "result": False,
                "data": {},
                "message": "No relevant information found"
            }, ensure_ascii=False)
            
    except Exception as e:
        return json.dumps({
            "result": False,
            "data": {},
            "message": f"Knowledge search failed: {str(e)}"
        }, ensure_ascii=False)

@mcp.tool()
def get_company_info(
    company_id: Annotated[int, "Company ID to get information for"]
) -> str:
    """获取公司基本信息"""
    try:
        # 这里可以扩展为从数据库或其他源获取公司信息
        # 目前返回模拟数据
        company_info = {
            1: {
                "name": "示例公司",
                "business": "零售连锁",
                "values": "客户至上，品质第一",
                "description": "专业的零售连锁企业，致力于为客户提供优质的产品和服务"
            }
        }
        
        info = company_info.get(company_id, {})
        if info:
            return json.dumps({
                "result": True,
                "data": info,
                "message": "Company information retrieved successfully"
            }, ensure_ascii=False)
        else:
            return json.dumps({
                "result": False,
                "data": {},
                "message": f"Company with ID {company_id} not found"
            }, ensure_ascii=False)
            
    except Exception as e:
        return json.dumps({
            "result": False,
            "data": {},
            "message": f"Failed to get company info: {str(e)}"
        }, ensure_ascii=False)

@mcp.tool()
def search_faq(
    company_id: Annotated[int, "Company ID for FAQ search"],
    question: Annotated[str, "User question to search in FAQ"]
) -> str:
    """在常见问题中搜索答案"""
    try:
        # 使用 RAG 搜索，但专门针对 FAQ 内容
        # 可以在关键词前添加 "FAQ" 或 "常见问题" 来优化搜索
        enhanced_query = f"FAQ 常见问题 {question}"
        result = rag_library.search_information(company_id, enhanced_query)
        
        if result:
            return json.dumps({
                "result": True,
                "data": {
                    "answer": result,
                    "question": question,
                    "company_id": company_id
                },
                "message": "FAQ search completed successfully"
            }, ensure_ascii=False)
        else:
            return json.dumps({
                "result": False,
                "data": {},
                "message": "No FAQ answer found for this question"
            }, ensure_ascii=False)
            
    except Exception as e:
        return json.dumps({
            "result": False,
            "data": {},
            "message": f"FAQ search failed: {str(e)}"
        }, ensure_ascii=False)

if __name__ == "__main__":
    mcp.run(transport="streamable-http")
