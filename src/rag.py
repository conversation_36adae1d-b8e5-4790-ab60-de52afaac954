import os
import requests

class VectorLibrary:

    def search_information(self, company_id, keywords):
        headers = {
            'Content-Type': 'application/json',
        }

        json_data = {
            "company_id": company_id,
            "query_text": keywords,
            "top_k": 3,
            "include_kg": 1,
            "rerank": 1,
            "optimize_query": 1
        }

        url = os.getenv("RAG_URL", 'http://20.106.73.120:5008/')
        request_url = url + '/search_knowledge'
        response = requests.post(request_url, headers=headers, json=json_data)

        if response.status_code == 200:
            response_data = response.json()
            data = response_data.get('data')
            return data['content']
        else:
            return ''
