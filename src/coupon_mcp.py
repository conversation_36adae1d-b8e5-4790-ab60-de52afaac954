import os
import string
import datetime
from typing import Annotated

import anyio
import pymysql
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pymysql.cursors import DictCursor

load_dotenv()

mcp = FastMCP(
    "CouponMCPServer",
    stateless_http=True,
    host="0.0.0.0",
    port=8001,
    streamable_http_path="/mcp"
)

# 自定义字符集，移除了 "O", "0", "I", "1"
base_chars_no_confusion = (
        "23456789" +  # 数字 (移除了0和1)
        string.ascii_lowercase +  # 小写字母 (a-z)
        "".join(c for c in string.ascii_uppercase if c not in ("I", "O"))  # 大写字母 (移除了I和O)
)
base = len(base_chars_no_confusion)  # 新的基数，即58
SECRET_KEY = int(os.getenv("SECRET_KEY"), 16)


def obfuscate_number(num: int, coupon_length: int) -> int:
    """
    对输入的数字进行混淆处理（模拟加密），使其不再呈现线性增长的规律，
    但依然保持唯一性和确定性。这不是密码学意义上的强加密，但足以打乱顺序。
    """
    # 确保数字在64位范围内操作，以避免Python大整数可能导致的性能问题
    num_64bit = num & 0xFFFFFFFFFFFFFFFF
    key_64bit = SECRET_KEY & 0xFFFFFFFFFFFFFFFF

    # 应用一系列位运算和乘法来混淆数字。
    # 这些操作是确定性的，意味着相同的输入总产生相同的输出。
    # 轮次1
    num_64bit = (num_64bit * 0xCBF29CE484222325) & 0xFFFFFFFFFFFFFFFF  # FNV prime
    num_64bit = (num_64bit ^ key_64bit) & 0xFFFFFFFFFFFFFFFF
    # 轮次2
    num_64bit = (num_64bit + 0x14D37B09) & 0xFFFFFFFFFFFFFFFF  # 另一个任意常数
    num_64bit = (num_64bit ^ (key_64bit >> 32)) & 0xFFFFFFFFFFFFFFFF
    # 轮次3
    num_64bit = (num_64bit * 0x100000001B3) & 0xFFFFFFFFFFFFFFFF  # FNV prime for 64-bit
    num_64bit = (num_64bit ^ (key_64bit << 16)) & 0xFFFFFFFFFFFFFFFF

    # 最终取模，确保混淆后的数字能被Base62编码为8位字符串
    # base ** coupon_length 是8位Base62能表示的最大数字 + 1
    max_encodable_value = base ** coupon_length
    return num_64bit % max_encodable_value


def base_encode(number: int) -> str:
    """
    将一个整数转换为Base62字符串。
    """
    if number == 0:
        return base_chars_no_confusion[0]  # 返回 "0"

    encoded_string = []
    while number > 0:
        encoded_string.append(base_chars_no_confusion[number % base])
        number //= base
    return "".join(reversed(encoded_string))


async def generate_and_insert_coupon(company_id: int, country_code: str, channel: str, contact_type: str, contact_value: str,
                               coupon_length: int) -> dict:
    """
    生成一个8位固定长度的唯一性优惠券码，并将其插入数据库。
    使用数据库的自增ID作为生成码的唯一基础。

    Args:
        company_id: 所属公司ID。
        country_code: 国家/地区代码。
        coupon_length: 优惠券码长度。
        channel: 发放渠道 phone/email/whatsapp 等
        contact_type: 联系方式类型 手机/邮箱/WhatsApp
        contact_value: 联系方式值 手机号/邮箱/WhatsApp号
    Returns:
        生成的优惠券码字符串，如果发生错误则返回 None。
    """
    conf = get_mysql_config()

    with pymysql.connect(**conf) as conn:
        cursor = conn.cursor(DictCursor)
        try:
            # 1. 插入一个记录，以获取数据库的自增ID
            # 我们可以先插入一个空的 coupon_code 或者一个状态，获取ID后更新
            now = datetime.datetime.now()
            init_data = {
                "company_id": company_id,  # 所属公司ID
                "contact_id": "",  # contact_id
                "coupon_code": "",  # 优惠券码
                "channel": channel,  # 发放渠道 phone/email/whatsapp 等
                "contact_type": contact_type,  # 联系方式类型 手机/邮箱/WhatsApp
                "contact_value": contact_value,  # 联系方式值 手机号/邮箱/WhatsApp号
                "country_code": country_code,  # 国家/地区代码
                "issue_time": now,  # 发放时间
                "check_time": now,  # 核销时间
                "check_store_id": 0,  # 核销门店id
                "check_store_uuid": "",  # 核销门店uuid
                "expire_time": now + datetime.timedelta(days=30),  # 过期时间
                "status": 0,  # 状态 0-未核销 1-已核销 2-已过期
                "deleted_by": None,  # 删除人（用户/系统ID）
                "deleted_at": None,  # 删除时间
            }

            # 先查询规则表，是否有自定义的规则
            query_sql = "SELECT limit_per_contact FROM coupon_generate_rule WHERE company_id = %s and status = %d and deleted_at is null"
            cursor.execute(query_sql, (init_data["company_id"], init_data["country_code"], 0, init_data["contact_type"]))
            result = cursor.fetchone()
            if result:
                limit_per_contact = result["limit_per_contact"]
            else:
                limit_per_contact = 1

            # 查询是否有重复的优惠券码生成过，如果有，则不生成，直接返回原码值
            now = datetime.datetime.now()
            # 计算本月的第一天
            start_of_this_month = datetime.datetime(now.year, now.month, 1)
            query_sql = "SELECT id, coupon_code, status, expire_time, check_time FROM coupon_issue_record WHERE issue_time > %s and contact_type = %s and contact_value = %s "
            cursor.execute(query_sql, (start_of_this_month, init_data["contact_type"], init_data["contact_value"]))
            result = cursor.fetchall()
            if len(result) >= limit_per_contact:
                # 说明这个联系方式在本月已经生成过优惠券码了
                return {
                    "result": False,
                    "data": {},
                    "message": "You have already received the coupon this month, please receive it next month"
                }
            # 获取刚刚插入行的自增ID，这将作为我们的唯一ID源
            db_id = insert_single_dict_to_mysql(conn, "coupon_issue_record", init_data)

            if not db_id:
                print("错误：无法获取数据库自增ID。")
                return {
                    "result": False,
                    "data": {},
                    "message": "Coupon generation failed"
                }

            # 2. 对数据库自增ID进行混淆（“加密”）
            obfuscated_number = obfuscate_number(db_id, coupon_length)

            # 3. 将混淆后的数字转换为Base62字符串
            coupon_code = base_encode(obfuscated_number)

            # 4. 填充以确保固定长度为8位
            # rjust() 会在左侧填充指定字符，直到达到目标长度
            final_coupon_code = coupon_code.rjust(coupon_length, base_chars_no_confusion[0])

            # 5. 更新数据库中的记录，将生成的优惠券码写入
            update_sql = "UPDATE coupon_issue_record SET coupon_code = %s WHERE id = %s"
            cursor.execute(update_sql, (final_coupon_code, db_id))
            conn.commit()  # 提交更新

            # return final_coupon_code
            return {
                "result": True,
                "data": {
                    "coupon_code": final_coupon_code,
                },
                "message": "Coupon generated successfully"
            }

        except pymysql.Error as e:
            conn.rollback()  # 回滚事务，防止部分操作成功
            print(f"数据库操作错误: {e}")
            return {
                    "result": False,
                    "data": {},
                    "message": f"Coupon generation failed: {e}"
                }
        except Exception as e:
            conn.rollback()  # 回滚事务
            print(f"生成优惠券时发生未知错误: {e}")
            return {
                    "result": False,
                    "data": {},
                    "message": f"Coupon generation failed: {e}"
                }
        finally:
            if cursor:
                cursor.close()


# 根据字典数据和插入表信息创建插入SQl语句
def insert_single_dict_to_mysql(conn, table_name: str, data: dict) -> int | bool:
    """
    将单个 Python 字典插入到 MySQL 表中。
    通过字典的键构建列名，通过字典的值构建值。
    """
    if not conn:
        print("数据库连接无效，无法执行插入操作。")
        return False

    # 提取字典的键作为列名
    columns = ", ".join(data.keys())
    # 为每个值生成占位符 %s
    placeholders = ", ".join(["%s"] * len(data))
    # 提取字典的值作为要插入的数据
    values = tuple(data.values())

    # 构建 INSERT 语句
    sql = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"

    cursor = None
    try:
        cursor = conn.cursor()
        cursor.execute(sql, values)
        conn.commit()  # 提交事务
        print(f"成功插入数据: {data}")
        # 获取刚刚插入行的自增ID，这将作为我们的唯一ID源
        db_id = cursor.lastrowid
        if db_id is None:
            print("错误：无法获取数据库自增ID。")
            return False
        return db_id
    except pymysql.err.IntegrityError as e:
        print(f"插入数据失败 (可能存在唯一键冲突): {e}. 数据: {data}")
        conn.rollback()  # 回滚事务
        return False
    except pymysql.Error as e:
        print(f"插入数据失败: {e}. SQL: {sql}, Values: {values}")
        conn.rollback()  # 回滚事务
        return False
    finally:
        if cursor:
            cursor.close()


def get_mysql_config():
    conf = dict(
        host=os.getenv("MYSQL_HOST"),
        port=int(os.getenv("MYSQL_PORT")),
        database=os.getenv("MYSQL_DATABASE"),
        user=os.getenv("MYSQL_USER"),
        password=os.getenv("MYSQL_PASSWD")
    )

    return conf


@mcp.tool()
async def generate_coupon(
        company_id: Annotated[int, "company_id from prompt"],
        country_code: Annotated[str, "country_code from user address"],
        channel: Annotated[str, "Software used by users"],
        contact_type: Annotated[str, "user's contact information type, only supports mobile phone number or WhatsApp"],
        contact_value: Annotated[str, "Contact information provided by the user"]
) -> str:
    """通过传入指定参数，生成一张优惠券，供用户进行使用，返回优惠券码值信息"""
    coupon_length = 8
    coupon = await anyio.to_thread.run_sync(generate_and_insert_coupon, company_id, country_code, channel, contact_type,
                                            contact_value, coupon_length)

    return coupon


if __name__ == "__main__":
    mcp.run(transport="streamable-http")
