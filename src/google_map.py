import json
import math
import os

import pymysql
import requests
from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel
from pymysql.cursors import DictCursor

load_dotenv()

mcp = FastMCP(
    "GoogleMapsMCPServer",
    stateless_http=True,
    debug=True,
    host="0.0.0.0",
    port=8000,
    streamable_http_path="/mcp"
)

GOOGLE_MAPS_API_KEY = os.getenv("GOOGLE_MAPS_API_KEY")
if not GOOGLE_MAPS_API_KEY:
    raise ValueError("GOOGLE_MAPS_API_KEY environment variable not set")

GEOCODING_API_URL = "https://maps.googleapis.com/maps/api/geocode/json"
STATIC_MAP_API_URL = "https://maps.googleapis.com/maps/api/staticmap"


def get_mysql_config():
    conf = dict(
        host=os.getenv('MYSQL_HOST'),
        port=int(os.getenv('MYSQL_PORT')),
        database=os.getenv('MYSQL_DATABASE'),
        user=os.getenv('MYSQL_USER'),
        password=os.getenv('MYSQL_PASSWD')
    )

    return conf


def get_mysql_connection(address, lat, lng, limit=3):
    conf = get_mysql_config()

    with pymysql.connect(**conf) as conn:
        cursor = conn.cursor(DictCursor)

        try:
            columns = ['id', 'name', 'address', 'latitude', 'longitude']
            sql = "SELECT " + ','.join(columns) + " FROM stores"
            cursor.execute(sql)
            record = cursor.fetchall()

            # 计算每个门店与输入地址的距离
            store_distances = []
            for store in record:
                distance = calculate_distance(lat, lng, store.get('latitude', 0.00), store.get('longitude', 0.00))
                store_distances.append((store, distance))

            # 按距离排序并返回最近的门店
            store_distances.sort(key=lambda x: x[1])
            nearest_stores = store_distances[:limit]

            # 格式化返回结果
            result = []
            for store, distance in nearest_stores:
                result.append({
                    "id": store.get('id', ''),
                    "name": store.get('name', ''),
                    "address": store.get('address', ''),
                    "latitude": store.get('latitude', ''),
                    "longitude": store.get('longitude', ''),
                    "distance": round(distance, 2)  # 距离保留两位小数
                })

            return {
                "input_address": address,
                "input_coordinates": {"latitude": lat, "longitude": lng},
                "nearby_stores": result
            }
        except BaseException as e:
            conn.rollback()
            raise Exception('最近3个点位获取失败：' + str(e))


def calculate_distance(lat1, lon1, lat2, lon2):
    """计算两个经纬度点之间的距离（使用Haversine公式）"""
    R = 6371  # 地球半径（公里）
    dlat = math.radians(lat2 - lat1)
    dlon = math.radians(lon2 - lon1)
    a = math.sin(dlat / 2) * math.sin(dlat / 2) + math.cos(math.radians(lat1)) \
        * math.cos(math.radians(lat2)) * math.sin(dlon / 2) * math.sin(dlon / 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
    distance = R * c
    return distance


@mcp.tool()
def geocode(address: str) -> str:
    """Geocodes an address string and returns the latitude and longitude."""
    params = {"address": address, "key": GOOGLE_MAPS_API_KEY}
    response = requests.get(GEOCODING_API_URL, params=params)
    response.raise_for_status()

    data = response.json()
    if data["status"] != "OK" or not data.get("results"):
        raise Exception(data.get("error_message", "Geocoding failed"))

    location = data["results"][0]["geometry"]["location"]
    return json.dumps({'latitude': location['lat'], 'longitude': location['lng']})


@mcp.tool()
def get_nearby_stores(address: str) -> dict:
    """Get the addresses and contact names of three nearby stores."""
    params = {"address": address, "key": GOOGLE_MAPS_API_KEY}
    response = requests.get(GEOCODING_API_URL, params=params)
    response.raise_for_status()

    data = response.json()
    if data["status"] != "OK" or not data.get("results"):
        raise Exception(data.get("error_message", "Geocoding failed"))

    location = data["results"][0]["geometry"]["location"]
    # location = {'lat': 30.49946, 'lng': 114.43011}
    result = get_mysql_connection(address=address, lat=location["lat"], lng=location["lng"], limit=3)
    result_arr = [{
        "store_name": doc.get('name'),
        "store_address": doc.get('address'),
        "country_code": doc.get('countryCode'),
        "timezone": doc.get('timezone'),
        "business_hours": doc.get('business_hours'),
        "store_latitude": doc.get('latitude'),
        "store_longitude": doc.get('longitude'),
        "store_distance": doc.get('distance')
    } for doc in result['nearby_stores']]
    return {
        "result": True,
        "data": result_arr,
        "message": "Successfully obtained the information of the nearest stores",
    }


if __name__ == "__main__":
    mcp.run(transport="streamable-http")
