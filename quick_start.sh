#!/bin/bash

# MCP 框架快速启动脚本

echo "🚀 MCP 多服务调用框架快速启动"
echo "=================================="

# 检查 Python 版本
python_version=$(python3 --version 2>&1 | grep -o '[0-9]\+\.[0-9]\+' | head -1)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 需要 Python 3.11+，当前版本: $python_version"
    exit 1
fi

echo "✅ Python 版本检查通过: $python_version"

# 检查并安装依赖
echo "📦 检查依赖..."
if [ ! -f "requirements_mcp.txt" ]; then
    echo "❌ 找不到 requirements_mcp.txt 文件"
    exit 1
fi

pip3 install -r requirements_mcp.txt

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到 .env 文件，创建示例文件..."
    cat > .env << EOF
# Google Maps API
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=coupon_db

# 优惠券生成密钥
SECRET_KEY=your_secret_key_in_hex

# RAG 服务
RAG_URL=http://*************:5008/

# Azure OpenAI 配置
AZURE_OPENAI_API_KEY=your_azure_openai_api_key
AZURE_OPENAI_ENDPOINT=https://your-endpoint.openai.azure.com
AZURE_OPENAI_MODEL=gpt-4o
EOF
    echo "📝 请编辑 .env 文件，填入正确的配置信息"
    echo "然后重新运行此脚本"
    exit 1
fi

echo "✅ 环境配置检查完成"

# 提供选项菜单
echo ""
echo "请选择运行模式:"
echo "1) 🎮 完整演示 (推荐) - 自动启动服务、运行测试、交互模式"
echo "2) 🔧 仅启动服务 - 启动所有 MCP 服务"
echo "3) 🧪 仅运行测试 - 测试框架功能"
echo "4) 🎯 仅运行示例 - 运行使用示例"
echo "5) ⚡ 快速测试 - 单个工具调用测试"

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "🎮 启动完整演示..."
        python3 run_mcp_demo.py
        ;;
    2)
        echo "🔧 启动 MCP 服务..."
        python3 start_all_services.py
        ;;
    3)
        echo "🧪 运行测试..."
        # 先在后台启动服务
        python3 start_all_services.py &
        SERVICE_PID=$!
        sleep 10  # 等待服务启动
        python3 test_mcp_framework.py
        kill $SERVICE_PID 2>/dev/null
        ;;
    4)
        echo "🎯 运行示例..."
        # 先在后台启动服务
        python3 start_all_services.py &
        SERVICE_PID=$!
        sleep 10  # 等待服务启动
        python3 example_usage.py
        kill $SERVICE_PID 2>/dev/null
        ;;
    5)
        echo "⚡ 快速测试..."
        cat > quick_test.py << 'EOF'
import asyncio
from multi_mcp_client import quick_call

async def main():
    try:
        print("🔍 测试地理编码工具...")
        result = await quick_call("geocode", address="北京市天安门广场")
        print(f"✅ 结果: {result}")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
EOF
        # 先在后台启动服务
        python3 start_all_services.py &
        SERVICE_PID=$!
        sleep 10  # 等待服务启动
        python3 quick_test.py
        kill $SERVICE_PID 2>/dev/null
        rm quick_test.py
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo "✅ 完成!"
